"""
API routes for Mai Voice Endpoints
Simplified routes for voice and chat functionality only
"""

import logging
import uuid
import json
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse

from config import settings, AVAILABLE_VOICES
from models import (
    ChatRequest, ChatResponse, VoiceRequest,
    SessionEndRequest, StatusResponse, SessionResponse,
    VoiceStatusResponse, HealthResponse,
    session_manager
)
from ai_handlers import ai_handler

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Health and Status Endpoints
@router.get("/")
async def root():
    """Root endpoint for basic connectivity"""
    return {"message": "Mai Voice Endpoints API", "status": "running", "version": "1.0"}

@router.get("/health")
async def health_check():
    """Health check endpoint for Railway deployment"""
    try:
        # Basic feature availability check
        features = {
            "text_chat": True,
            "voice_chat": True,
            "session_management": True
        }

        # Get active sessions count safely
        active_sessions = 0
        try:
            active_sessions = len(session_manager.get_active_sessions())
        except Exception:
            pass

        return HealthResponse(
            status="healthy",
            service="mai-voice-endpoints",
            version="1.0.0",
            features=features,
            active_sessions=active_sessions
        )
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return HealthResponse(
            status="unhealthy",
            service="mai-voice-endpoints",
            version="1.0.0",
            features={"text_chat": False, "voice_chat": False, "session_management": False},
            active_sessions=0
        )

# Chat Endpoints
@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Text chat with Mai"""
    try:
        logger.info(f"📨 Received chat request: {request.prompt[:50]}...")
        logger.info(f"🔍 Session ID: {request.session_id}")

        response = await ai_handler.generate_chat_response(request)

        logger.info(f"✅ Generated response: {response.response[:50]}...")
        return response
    except Exception as e:
        logger.error(f"❌ Chat endpoint error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to generate response")

# Test endpoint for debugging
@router.get("/chat/test")
async def chat_test():
    """Test endpoint to verify chat route is working"""
    return {"status": "ok", "message": "Chat endpoint is accessible", "timestamp": datetime.utcnow()}

# Voice Chat Endpoints
@router.get("/voice/status", response_model=VoiceStatusResponse)
async def voice_status():
    """Get voice chat status and available voices"""
    return VoiceStatusResponse(
        available=True,
        voice_name=settings.default_voice,
        webrtc_available=True,
        error=None
    )

@router.get("/voice/voices")
async def get_available_voices():
    """Get list of available voices"""
    return {"voices": AVAILABLE_VOICES}

@router.post("/voice/start", response_model=SessionResponse)
async def start_voice_chat(request: VoiceRequest):
    """Start voice chat session"""
    try:
        result = await ai_handler.start_voice_session(request)
        
        return SessionResponse(
            status=result["status"],
            session_id=result.get("session_id"),
            session_type="voice",
            message=result.get("message")
        )
    except Exception as e:
        logger.error(f"Voice start error: {e}")
        raise HTTPException(status_code=500, detail="Failed to start voice session")

# Session Management
@router.post("/session/end")
async def end_session(request: SessionEndRequest):
    """End a session"""
    try:
        if request.session_id:
            session_manager.end_session(request.session_id)
            return StatusResponse(
                status="success",
                message=f"Session {request.session_id} ended"
            )
        else:
            return StatusResponse(
                status="error",
                message="No session ID provided"
            )
    except Exception as e:
        logger.error(f"Session end error: {e}")
        raise HTTPException(status_code=500, detail="Failed to end session")

@router.get("/sessions/active")
async def get_active_sessions():
    """Get list of active sessions"""
    try:
        active_sessions = session_manager.get_active_sessions()
        return {
            "active_sessions": active_sessions,
            "count": len(active_sessions),
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error(f"Get active sessions error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active sessions")

# WebSocket Endpoints
@router.websocket("/ws/chat/{session_id}")
async def websocket_chat(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time chat"""
    await websocket.accept()
    logger.info(f"💬 WebSocket chat connection established for session: {session_id}")
    
    try:
        # Get or create session
        memory = session_manager.get_session(session_id)
        if not memory:
            session_manager.sessions[session_id] = session_manager.sessions.get(
                session_id, 
                session_manager.sessions.setdefault(session_id, type(session_manager.sessions[list(session_manager.sessions.keys())[0]] if session_manager.sessions else None)(session_id))
            )
            memory = session_manager.get_session(session_id)
        
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            logger.info(f"💬 Received WebSocket message: {data}")

            if data.get("type") == "chat":
                # Handle chat message
                user_message = data.get("message", "")
                
                if user_message.strip():
                    # Add user message to memory
                    memory.add_message("user", user_message)
                    
                    # Generate AI response
                    chat_request = ChatRequest(
                        prompt=user_message,
                        session_id=session_id
                    )
                    
                    response = await ai_handler.generate_chat_response(chat_request)
                    
                    # Send response back to client
                    await websocket.send_json({
                        "type": "chat_response",
                        "message": response.response,
                        "session_id": session_id,
                        "timestamp": response.timestamp.isoformat()
                    })

    except WebSocketDisconnect:
        logger.info(f"💬 WebSocket chat disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket chat error: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "message": "Connection error occurred"
            })
        except:
            pass

@router.websocket("/ws/voice/{session_id}")
async def websocket_voice(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for voice communication"""
    await websocket.accept()
    logger.info(f"🎤 WebSocket voice connection established for session: {session_id}")
    
    try:
        # Get or create session
        memory = session_manager.get_session(session_id)
        if not memory:
            session_manager.sessions[session_id] = session_manager.sessions.get(
                session_id,
                type(session_manager.sessions[list(session_manager.sessions.keys())[0]] if session_manager.sessions else None)(session_id)
            )

        while True:
            # Receive audio data or control messages
            data = await websocket.receive_text()
            message_data = json.loads(data)

            if message_data.get("type") == "audio_data":
                # Process audio input and generate voice response
                audio_response = await ai_handler.process_voice_input(
                    session_id,
                    message_data.get("audio_data")
                )

                # Send voice response back
                await websocket.send_text(json.dumps({
                    "type": "voice_response",
                    "audio_data": audio_response.get("audio_data"),
                    "text": audio_response.get("text"),
                    "session_id": session_id
                }))

            elif message_data.get("type") == "voice_end":
                # End voice session
                await ai_handler.end_voice_session(session_id)
                await websocket.send_text(json.dumps({
                    "type": "voice_ended",
                    "message": "Voice session ended"
                }))
                break

    except WebSocketDisconnect:
        logger.info(f"🎤 WebSocket voice disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"❌ WebSocket voice error: {e}")
        try:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Voice connection error occurred"
            }))
        except:
            pass
