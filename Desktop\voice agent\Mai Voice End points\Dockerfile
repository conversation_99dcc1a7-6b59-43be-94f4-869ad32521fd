# Use Python 3.11 slim for smaller image size
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    BUILD_VERSION=2025-01-01-mai-endpoints-v1.0 \
    PYTHONPATH=/app \
    HOST=0.0.0.0 \
    PORT=8000 \
    DEBUG=false \
    GEMINI_API_KEY=dummy-key-for-health-check

# Set work directory
WORKDIR /app

# Install system dependencies for audio processing and ML
RUN apt-get update && apt-get install -y \
    # Build tools
    build-essential \
    cmake \
    pkg-config \
    # Audio libraries for Whisper
    ffmpeg \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libswresample-dev \
    # Audio processing
    portaudio19-dev \
    libasound2-dev \
    libsndfile1-dev \
    # SSL and crypto
    libssl-dev \
    libffi-dev \
    # Python development
    python3-dev \
    # Utilities
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies in stages for better caching
RUN echo "Installing Mai Voice Endpoints dependencies - Build $(date)" && \
    # Install core dependencies first
    pip install --no-cache-dir fastapi==0.104.1 uvicorn[standard]==0.24.0 && \
    pip install --no-cache-dir google-genai>=0.8.0 google-generativeai>=0.3.2 && \
    pip install --no-cache-dir python-dotenv pydantic pydantic-settings && \
    pip install --no-cache-dir httpx aiofiles python-multipart websockets && \
    pip install --no-cache-dir gunicorn psutil && \
    # Install ML/Audio dependencies
    pip install --no-cache-dir torch torchaudio --index-url https://download.pytorch.org/whl/cpu && \
    pip install --no-cache-dir openai-whisper && \
    pip install --no-cache-dir numpy scipy librosa soundfile && \
    # Install communication dependencies
    pip install --no-cache-dir aiosmtplib email-validator || echo "⚠️ Some optional dependencies failed" && \
    echo "✅ Core dependencies installed successfully"

# Verify core imports for basic system
RUN python -c "import fastapi, uvicorn; print('✅ Core FastAPI imports OK')" && \
    python -c "from pydantic import BaseModel; print('✅ Pydantic import OK')" && \
    python -c "import asyncio; print('✅ Asyncio import OK')" && \
    python -c "import openai; print('✅ OpenAI client import OK')" || echo "⚠️ Some imports failed, continuing..."

# Copy application code
COPY . .

# Test that our models work correctly
RUN python -c "from models import ChatRequest, ChatResponse; print('✅ Models import successfully')" && \
    python -c "from config import settings; print('✅ Config import successfully')" && \
    python -c "from ai_handlers import ai_handler; print('✅ AI handlers import successfully')" && \
    python -c "from summarization_service import summarization_service; print('✅ Summarization service import OK')" && \
    python -c "from email_service import email_service; print('✅ Email service import OK')" && \
    python -c "from main import app; print('✅ Main app import OK')" || echo "⚠️ Some services not available, continuing..."

# Create non-root user for security and set up Whisper cache directory
RUN groupadd -r appuser && useradd -r -g appuser appuser && \
    mkdir -p /home/<USER>/.cache/whisper /tmp/whisper_cache && \
    chown -R appuser:appuser /app /home/<USER>
    chmod 755 /tmp/whisper_cache
USER appuser

# Set Whisper cache directory with fallback
ENV XDG_CACHE_HOME=/home/<USER>/.cache
ENV WHISPER_CACHE_DIR=/tmp/whisper_cache

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Command to run the application
CMD ["python", "main.py"]
