/**
 * Mai Voice Plugin JavaScript
 * Enhanced with real-time voice-to-voice functionality
 */

(function($) {
    'use strict';

    class MaiVoiceChat {
        constructor() {
            this.isOpen = false;
            this.isRecording = false;
            this.mediaRecorder = null;
            this.audioChunks = [];
            this.currentSessionId = null;
            this.audioContext = null;
            this.isPlaying = false;
            this.peerConnection = null;
            this.localStream = null;
            this.webrtcId = null;

            this.init();
        }

        init() {
            this.createChatWidget();
            this.bindEvents();
            this.initializeAudioContext();
        }

        async initializeAudioContext() {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (error) {
                console.warn('Audio context not supported:', error);
            }
        }

        createChatWidget() {
            const config = window.maiVoiceConfig;

            const widget = `
                <div id="mai-voice-widget" class="mai-voice-widget">
                    <div id="mai-chat-toggle" class="mai-chat-toggle">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2C6.48 2 2 6.48 2 12C2 13.54 2.36 14.99 3.01 16.28L2 22L7.72 20.99C9.01 21.64 10.46 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2Z" fill="currentColor"/>
                        </svg>
                    </div>

                    <div id="mai-chat-window" class="mai-chat-window" style="display: none;">
                        <div class="mai-chat-header">
                            <div class="mai-chat-title">
                                <div class="mai-avatar">🎤</div>
                                <div>
                                    <h4>${config.strings.title}</h4>
                                    <span class="mai-status" id="mai-status">Ready</span>
                                </div>
                            </div>
                            <button id="mai-chat-close" class="mai-chat-close">×</button>
                        </div>

                        <div class="mai-chat-messages" id="mai-chat-messages">
                            <div class="mai-message mai-message-assistant">
                                <div class="mai-message-content">
                                    Hi! I'm Mai, your AI voice assistant. Click the microphone to start talking with me! 🎤
                                </div>
                                <div class="mai-message-time">${new Date().toLocaleTimeString()}</div>
                            </div>
                        </div>

                        <div class="mai-chat-input">
                            <div class="mai-input-container">
                                <input type="text" id="mai-text-input" placeholder="${config.strings.placeholder}" />
                                <button id="mai-voice-btn" class="mai-voice-btn" title="Voice Message">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2C10.34 2 9 3.34 9 5V11C9 12.66 10.34 14 12 14Z" fill="currentColor"/>
                                        <path d="M17 11C17 14.53 14.39 17.44 11 17.93V21H13V23H11H9V21H11V17.93C7.61 17.44 5 14.53 5 11H7C7 13.76 9.24 16 12 16C14.76 16 17 13.76 17 11H17Z" fill="currentColor"/>
                                    </svg>
                                </button>
                                <button id="mai-send-btn" class="mai-send-btn" title="Send Message">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" fill="currentColor"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="mai-voice-controls" id="mai-voice-controls" style="display: none;">
                            <div class="mai-recording-indicator">
                                <div class="mai-pulse"></div>
                                <span>Recording... Click to stop</span>
                            </div>
                            <button id="mai-stop-recording" class="mai-stop-btn">Stop Recording</button>
                        </div>
                    </div>
                </div>
            `;

            $('#mai-voice-chat-container').html(widget);
        }

        bindEvents() {
            const self = this;

            $(document).on('click', '#mai-chat-toggle', function() {
                self.toggleChat();
            });

            $(document).on('click', '#mai-chat-close', function() {
                self.closeChat();
            });

            $(document).on('click', '#mai-voice-btn', function() {
                self.toggleVoiceRecording();
            });

            $(document).on('click', '#mai-stop-recording', function() {
                self.stopRecording();
            });

            $(document).on('click', '#mai-send-btn', function() {
                self.sendTextMessage();
            });

            $(document).on('keypress', '#mai-text-input', function(e) {
                if (e.which === 13) {
                    self.sendTextMessage();
                }
            });
        }

        toggleChat() {
            if (this.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }

        openChat() {
            $('#mai-chat-window').slideDown(300);
            $('#mai-chat-toggle').addClass('active');
            this.isOpen = true;
        }

        closeChat() {
            $('#mai-chat-window').slideUp(300);
            $('#mai-chat-toggle').removeClass('active');
            this.isOpen = false;

            if (this.currentSessionId) {
                this.endSession();
            }
        }

        async toggleVoiceRecording() {
            if (this.isRecording) {
                this.stopRecording();
            } else {
                await this.startRecording();
            }
        }

        async startRecording() {
            try {
                // Start WebRTC voice session
                await this.startWebRTCSession();
                this.updateUI('recording');

            } catch (error) {
                console.error('Error starting WebRTC session:', error);
                this.showError(window.maiVoiceConfig.strings.microphoneError);
            }
        }

        async startWebRTCSession() {
            try {
                // Generate session ID and WebRTC ID
                this.currentSessionId = this.generateSessionId();
                this.webrtcId = Math.random().toString(36).substring(7);

                this.updateUI('connecting');

                // Set input parameters for WebRTC session
                const inputResponse = await this.makeAPIRequest('input_hook', {
                    webrtc_id: this.webrtcId,
                    voice_name: 'Aoede',
                    mode: 'audio'
                });

                if (inputResponse.success && inputResponse.data.status === 'error') {
                    throw new Error(inputResponse.data.message || 'Failed to initialize voice session');
                }

                // Initialize WebRTC peer connection
                this.peerConnection = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                });

                // Get microphone access
                this.localStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 16000
                    }
                });

                // Add local stream to peer connection
                this.localStream.getTracks().forEach(track => {
                    this.peerConnection.addTrack(track, this.localStream);
                });

                // Handle remote audio stream (Mai's voice)
                this.peerConnection.ontrack = (event) => {
                    const [remoteStream] = event.streams;
                    this.playRemoteAudio(remoteStream);
                };

                // Handle ICE candidates
                this.peerConnection.onicecandidate = ({ candidate }) => {
                    if (candidate) {
                        console.debug("Sending ICE candidate", candidate);
                        fetch(`${window.maiVoiceConfig.apiEndpoint}/audio/webrtc/offer`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                candidate: candidate.toJSON(),
                                webrtc_id: this.webrtcId,
                                type: "ice-candidate",
                            })
                        }).catch(e => console.error("Error sending ICE candidate:", e));
                    }
                };

                // Create and send WebRTC offer
                const offer = await this.peerConnection.createOffer();
                await this.peerConnection.setLocalDescription(offer);

                const response = await fetch(`${window.maiVoiceConfig.apiEndpoint}/audio/webrtc/offer`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: this.peerConnection.localDescription.sdp,
                        type: this.peerConnection.localDescription.type,
                        webrtc_id: this.webrtcId,
                    })
                });

                const serverResponse = await response.json();

                if (serverResponse.status === 'failed') {
                    const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many active sessions. Please try again later.`
                        : serverResponse.meta.message || 'WebRTC connection failed';
                    throw new Error(errorMsg);
                }

                if (serverResponse.sdp) {
                    await this.peerConnection.setRemoteDescription(new RTCSessionDescription({
                        type: serverResponse.type,
                        sdp: serverResponse.sdp
                    }));
                }

                this.isRecording = true;
                this.updateUI('recording');
                this.addMessage('system', 'Voice chat connected! Start speaking with Mai.');

            } catch (error) {
                console.error('WebRTC setup error:', error);
                this.updateUI('error');
                throw error;
            }
        }

        playRemoteAudio(stream) {
            try {
                // Create audio element for remote stream (Mai's voice)
                const audioElement = document.createElement('audio');
                audioElement.srcObject = stream;
                audioElement.autoplay = true;
                audioElement.volume = 1.0;

                // Add to DOM temporarily for playback
                document.body.appendChild(audioElement);

                // Remove after playback ends
                audioElement.onended = () => {
                    if (audioElement.parentNode) {
                        audioElement.parentNode.removeChild(audioElement);
                    }
                };

                this.updateUI('playing');

                // Reset to ready state after a short delay
                setTimeout(() => {
                    if (!this.isRecording) return;
                    this.updateUI('recording');
                }, 2000);

            } catch (error) {
                console.error('Error playing remote audio:', error);
            }
        }

        generateSessionId() {
            return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        }

        stopRecording() {
            if (this.isRecording) {
                this.isRecording = false;
                this.updateUI('processing');

                // Close WebRTC connection
                this.closeWebRTCSession();
            }
        }

        closeWebRTCSession() {
            try {
                // Stop local stream
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                }

                // Close peer connection
                if (this.peerConnection) {
                    this.peerConnection.close();
                    this.peerConnection = null;
                }

                this.updateUI('ready');
                this.addMessage('system', 'Voice chat ended.');

            } catch (error) {
                console.error('Error closing WebRTC session:', error);
                this.updateUI('ready');
            }
        }

        async sendTextMessage() {
            const input = $('#mai-text-input');
            const message = input.val().trim();

            if (!message) return;

            input.val('');
            this.addMessage('user', message);
            this.updateUI('processing');

            try {
                const response = await this.makeAPIRequest('chat', {
                    message: message,
                    session_id: this.currentSessionId
                });

                if (response.success) {
                    const data = response.data;
                    this.currentSessionId = data.session_id;
                    this.addMessage('assistant', data.response);
                } else {
                    this.showError('Failed to send message');
                }

            } catch (error) {
                console.error('Error sending message:', error);
                this.showError('Error sending message');
            }

            this.updateUI('ready');
        }

        addMessage(role, content) {
            const messagesContainer = $('#mai-chat-messages');
            const time = new Date().toLocaleTimeString();

            const messageHtml = `
                <div class="mai-message mai-message-${role}">
                    <div class="mai-message-content">${this.escapeHtml(content)}</div>
                    <div class="mai-message-time">${time}</div>
                </div>
            `;

            messagesContainer.append(messageHtml);
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
        }

        updateUI(state) {
            const status = $('#mai-status');
            const voiceControls = $('#mai-voice-controls');
            const inputContainer = $('.mai-input-container');

            switch (state) {
                case 'recording':
                    status.text('Recording...');
                    voiceControls.show();
                    inputContainer.hide();
                    $('#mai-voice-btn').addClass('recording');
                    break;
                case 'processing':
                    status.text('Processing...');
                    voiceControls.hide();
                    inputContainer.show();
                    $('#mai-voice-btn').removeClass('recording');
                    break;
                case 'playing':
                    status.text('Mai is speaking...');
                    break;
                case 'ready':
                default:
                    status.text('Ready');
                    voiceControls.hide();
                    inputContainer.show();
                    $('#mai-voice-btn').removeClass('recording');
                    break;
            }
        }

        async endSession() {
            if (this.currentSessionId || this.webrtcId) {
                try {
                    // Close WebRTC connection first
                    this.closeWebRTCSession();

                    // End session on server (this will trigger transcription and email)
                    const sessionId = this.webrtcId || this.currentSessionId;
                    await this.makeAPIRequest('session_end', {
                        session_id: sessionId,
                        email_address: '<EMAIL>' // You can make this configurable
                    });

                    this.addMessage('system', window.maiVoiceConfig.strings.sessionEnded);
                    this.currentSessionId = null;
                    this.webrtcId = null;
                } catch (error) {
                    console.error('Error ending session:', error);
                }
            }
        }

        showError(message) {
            this.addMessage('system', `❌ ${message}`);
        }

        async blobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        async makeAPIRequest(action, data) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: window.maiVoiceConfig.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'mai_voice_proxy',
                        nonce: window.maiVoiceConfig.nonce,
                        mai_action: action,
                        data: data
                    },
                    success: resolve,
                    error: reject
                });
            });
        }
    }

    $(document).ready(function() {
        if (window.maiVoiceConfig && window.maiVoiceConfig.enabled === '1') {
            new MaiVoiceChat();
        }
    });

})(jQuery);
