"""
Data models for Mai Voice Endpoints
Simplified models for voice and chat functionality only
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid
import asyncio
from collections import defaultdict

class SessionType(str, Enum):
    """Types of communication sessions"""
    TEXT = "text"
    VOICE = "voice"

class SessionStatus(str, Enum):
    """Session status states"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"

class MessageType(str, Enum):
    """Types of messages in conversation"""
    USER = "user"
    MAI = "mai"
    SYSTEM = "system"

# Request Models
class ChatRequest(BaseModel):
    """Request model for text chat"""
    prompt: str = Field(..., min_length=1, max_length=2000, description="User message")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="AI response creativity")
    max_tokens: Optional[int] = Field(512, ge=1, le=2048, description="Maximum response length")
    session_id: Optional[str] = Field(None, description="Session identifier")

class VoiceRequest(BaseModel):
    """Request model for voice chat setup"""
    webrtc_id: str = Field(..., description="WebRTC connection identifier")
    voice_name: str = Field("Aoede", description="Voice to use for responses")
    mode: str = Field("audio", description="Communication mode")
    session_id: Optional[str] = Field(None, description="Session identifier")

class SessionEndRequest(BaseModel):
    """Request to end a session"""
    session_id: Optional[str] = Field(None, description="Session to end")
    reason: Optional[str] = Field("user_request", description="Reason for ending session")

# Response Models
class ChatResponse(BaseModel):
    """Response model for text chat"""
    response: str = Field(..., description="AI response message")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")

class StatusResponse(BaseModel):
    """General status response"""
    status: str = Field(..., description="Status message")
    message: Optional[str] = Field(None, description="Additional information")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Status timestamp")

class SessionResponse(BaseModel):
    """Session operation response"""
    status: str = Field(..., description="Operation status")
    session_id: Optional[str] = Field(None, description="Session identifier")
    session_type: Optional[SessionType] = Field(None, description="Type of session")
    message: Optional[str] = Field(None, description="Status message")

class VoiceStatusResponse(BaseModel):
    """Voice chat status response"""
    available: bool = Field(..., description="Whether voice chat is available")
    voice_name: str = Field("Aoede", description="Current voice setting")
    webrtc_available: bool = Field(..., description="WebRTC availability")
    error: Optional[str] = Field(None, description="Error message if any")

class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Service health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    service: str = Field("mai-voice-endpoints", description="Service name")
    version: str = Field("1.0.0", description="Service version")
    features: Dict[str, bool] = Field(default_factory=dict, description="Available features")
    active_sessions: int = Field(0, description="Number of active sessions")

# Conversation Memory
class ConversationMemory:
    """Simple conversation memory for sessions"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.messages: List[Dict[str, Any]] = []
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
    
    def add_message(self, role: str, content: str):
        """Add a message to conversation history"""
        self.messages.append({
            "role": role,
            "content": content,
            "timestamp": datetime.utcnow()
        })
        self.last_activity = datetime.utcnow()
    
    def get_recent_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent messages for context"""
        return self.messages[-limit:] if self.messages else []
    
    def clear(self):
        """Clear conversation history"""
        self.messages.clear()

# Session Manager
class SessionManager:
    """Manages active sessions and conversation memory"""
    
    def __init__(self):
        self.sessions: Dict[str, ConversationMemory] = {}
        self.session_types: Dict[str, SessionType] = {}
        self.cleanup_task = None
    
    def create_session(self, session_type: SessionType = SessionType.TEXT) -> str:
        """Create a new session"""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = ConversationMemory(session_id)
        self.session_types[session_id] = session_type
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ConversationMemory]:
        """Get session by ID"""
        return self.sessions.get(session_id)
    
    def end_session(self, session_id: str):
        """End a session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
        if session_id in self.session_types:
            del self.session_types[session_id]
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        return list(self.sessions.keys())
    
    def cleanup_expired_sessions(self, timeout_seconds: int = 300):
        """Remove expired sessions"""
        current_time = datetime.utcnow()
        expired_sessions = []
        
        for session_id, memory in self.sessions.items():
            if (current_time - memory.last_activity).total_seconds() > timeout_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.end_session(session_id)

# Global session manager instance
session_manager = SessionManager()
