# Mai Voice Plugin

A lightweight, responsive voice popup chat plugin that integrates with the Mai Voice Endpoints API.

## Features

- 💬 Text chat with Mai AI assistant
- 🎤 Voice recording capability
- 📱 Mobile responsive design
- 🔄 Real-time WebSocket communication
- 🎨 Beautiful, modern UI
- ⚡ Easy integration with any website

## Quick Start

1. **Include the plugin files in your HTML:**

```html
<!-- Add to your HTML head -->
<link rel="stylesheet" href="mai-voice-plugin.css">

<!-- Add before closing body tag -->
<script>
  // Optional: Configure API URL
  window.maiApiUrl = 'https://your-api-domain.com';
</script>
<script src="mai-voice-plugin.js"></script>
```

2. **The plugin will automatically initialize** and add a floating chat button to your page.

## Configuration

You can configure the plugin by setting global variables before loading the script:

```javascript
// API endpoint URL
window.maiApiUrl = 'https://your-mai-api.com';

// Disable auto-initialization
window.maiPluginDisabled = true;

// Manual initialization
const plugin = new MaiVoicePlugin({
    apiUrl: 'https://your-mai-api.com'
});
```

## API Requirements

The plugin requires the Mai Voice Endpoints API to be running with these endpoints:

- `POST /chat` - Text chat
- `GET /health` - Health check
- `WebSocket /ws/chat/{session_id}` - Real-time chat
- `WebSocket /ws/voice/{session_id}` - Voice communication

## Demo

Open `index.html` in your browser to see the plugin in action. Make sure the Mai Voice Endpoints API is running on `localhost:8000`.

## Customization

### Styling

Modify `mai-voice-plugin.css` to customize the appearance:

- Change colors by updating the gradient values
- Adjust positioning by modifying the `.mai-plugin-container` styles
- Customize animations and transitions

### Functionality

Extend the plugin by modifying `mai-voice-plugin.js`:

- Add new message types
- Implement custom voice processing
- Add file upload capabilities
- Integrate with other services

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## License

MIT License
