# Mai Voice Endpoints

Backend API endpoints for Mai voice and chat functionality, designed for deployment to Railway and integration with voice popup plugins.

## Features

- Text chat with Mai AI
- Voice chat capabilities
- WebSocket real-time communication
- Health monitoring
- Railway deployment ready
- Plugin integration support

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. Run locally:
```bash
python main.py
```

## API Endpoints

- `POST /chat` - Text chat with Mai
- `GET /voice/status` - Voice chat status
- `GET /voice/voices` - Available voices
- `POST /voice/start` - Start voice session
- `WebSocket /ws/chat/{session_id}` - Real-time chat
- `WebSocket /ws/voice/{session_id}` - Voice communication
- `GET /health` - Health check

## Environment Variables

```bash
GEMINI_API_KEY=your_gemini_api_key
PORT=8000
HOST=0.0.0.0
DEBUG=false
```

## Deployment

Deploy to <PERSON> with one click or use the included Dockerfile.

## Plugin

See the `plugin/` directory for the voice popup chat plugin that uses these endpoints.

## License

MIT License
