# Mai Voice Endpoints

Backend API endpoints for Mai voice and chat functionality, designed for deployment to Railway and integration with voice popup plugins.

## Features

- **Text chat with <PERSON> AI** - Powered by Google Gemini
- **Voice chat with transcription** - Using OpenAI Whisper for speech-to-text
- **Conversation summarization** - Using Deepseek API for intelligent summaries
- **Email notifications** - Automatic email delivery of conversation summaries
- **WebSocket real-time communication** - For live chat and voice sessions
- **Complete workflow automation** - Voice → Transcription → Summarization → Email
- **Health monitoring** - Service status and diagnostics
- **Railway deployment ready** - Production-ready containerization
- **Plugin integration support** - Easy integration with websites

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. Run locally:
```bash
python main.py
```

## API Endpoints

### Core Endpoints
- `POST /chat` - Text chat with Mai
- `GET /voice/status` - Voice chat status
- `GET /voice/voices` - Available voices
- `POST /voice/start` - Start voice session
- `POST /session/end` - End session (triggers workflow for voice sessions)
- `GET /sessions/active` - List active sessions

### WebSocket Endpoints
- `WebSocket /ws/chat/{session_id}` - Real-time text chat
- `WebSocket /ws/voice/{session_id}` - Voice communication with transcription

### Service Management
- `GET /health` - Health check
- `GET /services/status` - Status of all integrated services
- `POST /test/email` - Test email service
- `POST /test/transcription` - Test transcription service

## Voice Chat Workflow

1. **Start Session** - `POST /voice/start` creates a voice session
2. **Voice Input** - Send audio data via WebSocket to `/ws/voice/{session_id}`
3. **Transcription** - Audio is automatically transcribed using Whisper
4. **AI Response** - Mai generates response using transcribed text
5. **End Session** - When session ends, automatic workflow triggers:
   - **Summarization** - Deepseek creates conversation summary
   - **Email Delivery** - Summary is emailed to configured address

## Environment Variables

```bash
# Required API Keys
GEMINI_API_KEY=your_gemini_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-gmail-app-password

# Optional Configuration
PORT=8000
HOST=0.0.0.0
DEBUG=false
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
```

## Deployment

Deploy to Railway with one click or use the included Dockerfile.

## Plugin

See the `plugin/` directory for the voice popup chat plugin that uses these endpoints.

## License

MIT License
