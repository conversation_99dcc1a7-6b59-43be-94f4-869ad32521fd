#!/usr/bin/env python3
"""
Simple startup script for Mai Voice Agent
Handles environment setup and starts the server
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment variables"""
    # Add backend to Python path
    backend_path = Path(__file__).parent / "backend"
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
    
    # Set default environment variables
    os.environ.setdefault("HOST", "0.0.0.0")
    os.environ.setdefault("PORT", "8000")
    os.environ.setdefault("PYTHONUNBUFFERED", "1")
    
    # Check for API key
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key or api_key == "dummy-key-for-railway-health-check":
        logger.warning("⚠️ No valid GEMINI_API_KEY found - AI features will be limited")
        logger.info("💡 Set GEMINI_API_KEY environment variable for full functionality")
    else:
        logger.info("✅ GEMINI_API_KEY found")
    
    logger.info(f"🌐 Server will start on {os.environ.get('HOST')}:{os.environ.get('PORT')}")

def main():
    """Main startup function"""
    logger.info("🚀 Starting Mai Voice Agent...")
    
    try:
        # Setup environment
        setup_environment()
        
        # Import and run the railway server
        from railway_server import main as railway_main
        railway_main()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
