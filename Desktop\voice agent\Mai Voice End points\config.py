"""
Configuration module for Mai Voice Endpoints
Simplified configuration for voice and chat endpoints only
"""

import os
import logging
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings with environment variable support"""

    # Gemini API Configuration
    gemini_api_key: str = Field("dummy-key-for-health-check", env="GEMINI_API_KEY")
    
    # Model Configuration
    text_chat_model: str = Field("gemini-1.5-flash", env="TEXT_CHAT_MODEL")
    voice_chat_model: str = Field("gemini-2.0-flash-exp", env="VOICE_CHAT_MODEL")

    # Server Configuration
    port: int = Field(8000, env="PORT")
    host: str = Field("0.0.0.0", env="HOST")
    debug: bool = Field(False, env="DEBUG")

    # Voice Configuration
    default_voice: str = Field("Aoede", env="DEFAULT_VOICE")

    # Session Configuration
    session_timeout: int = Field(300, env="SESSION_TIMEOUT")  # 5 minutes
    max_concurrent_sessions: int = Field(10, env="MAX_CONCURRENT_SESSIONS")

    class Config:
        env_file = ".env"
        case_sensitive = False

# Create settings instance
settings = Settings()

# Available voices for voice chat
AVAILABLE_VOICES = [
    {"name": "Aoede", "description": "Warm and friendly voice"},
    {"name": "Charon", "description": "Deep and authoritative voice"},
    {"name": "Kore", "description": "Clear and professional voice"},
    {"name": "Fenrir", "description": "Strong and confident voice"},
    {"name": "Puck", "description": "Playful and energetic voice"}
]

# System prompt for Mai
SYSTEM_PROMPT = """You are Mai, an AI assistant created by Critical Future. You are helpful, friendly, and knowledgeable. 

Key traits:
- Professional yet approachable
- Concise but thorough responses
- Helpful and solution-oriented
- Knowledgeable about technology and business

Keep responses conversational and engaging. If asked about voice capabilities, explain that you can communicate through both text and voice chat."""

def validate_configuration() -> bool:
    """Validate that required configuration is present"""
    try:
        if settings.gemini_api_key == "dummy-key-for-health-check":
            logger.warning("⚠️ Using dummy Gemini API key - some features may not work")
            return False
        
        logger.info("✅ Configuration validation passed")
        return True
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False
