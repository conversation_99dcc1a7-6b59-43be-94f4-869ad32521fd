"""
Main FastAPI application for Mai Voice Endpoints
Simplified application for voice and chat endpoints only
"""

import os
import logging
import asyncio
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config import settings, validate_configuration
from routes import router
from models import session_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def cleanup_task():
    """Background task to clean up expired sessions"""
    while True:
        try:
            await asyncio.sleep(60)  # Run every minute
            session_manager.cleanup_expired_sessions(settings.session_timeout)
            logger.debug("🧹 Session cleanup completed")
        except Exception as e:
            logger.error(f"❌ Session cleanup error: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("🚀 Starting Mai Voice Endpoints...")

    try:
        # Validate configuration (lenient for Railway)
        if not validate_configuration():
            logger.warning("⚠️ Configuration validation failed - continuing with limited features")

        # Start background tasks
        cleanup_task_handle = None
        try:
            cleanup_task_handle = asyncio.create_task(cleanup_task())
            logger.info("✅ Background cleanup task started")
        except Exception as e:
            logger.warning(f"⚠️ Background task failed to start: {e}")

        logger.info("✅ Mai Voice Endpoints started successfully")

        yield

    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        # Don't fail startup for Railway deployment
        logger.info("✅ Continuing with basic functionality")
        yield

    finally:
        # Shutdown
        logger.info("🛑 Shutting down Mai Voice Endpoints...")
        if 'cleanup_task_handle' in locals() and cleanup_task_handle:
            cleanup_task_handle.cancel()
            try:
                await cleanup_task_handle
            except asyncio.CancelledError:
                pass
        logger.info("✅ Mai Voice Endpoints shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Mai Voice Endpoints",
    description="AI Voice and Chat Endpoints for Mai Assistant",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routes
app.include_router(router)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors"""
    logger.error(f"❌ Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# Middleware for request logging
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all HTTP requests"""
    start_time = datetime.utcnow()
    
    # Log request
    logger.info(f"📥 {request.method} {request.url.path}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = (datetime.utcnow() - start_time).total_seconds()
    logger.info(f"📤 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
    
    return response

# Main entry point
if __name__ == "__main__":
    import uvicorn
    
    # Railway deployment: Use PORT environment variable if available
    port = int(os.environ.get("PORT", settings.port))
    host = os.environ.get("HOST", settings.host)

    logger.info(f"🚀 Starting Mai Voice Endpoints on {host}:{port}")
    logger.info(f"🔧 Environment: PORT={os.environ.get('PORT')}, HOST={os.environ.get('HOST')}")
    logger.info(f"⚙️ Settings: port={settings.port}, host={settings.host}")
    logger.info(f"🌐 Server will be accessible at http://{host}:{port}")

    # For Railway, ensure we bind to all interfaces
    if host == "localhost" or host == "127.0.0.1":
        logger.warning("⚠️ Changing host from localhost to 0.0.0.0 for Railway compatibility")
        host = "0.0.0.0"

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=settings.debug,
        log_level="info"
    )
