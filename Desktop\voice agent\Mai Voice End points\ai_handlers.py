"""
AI handlers for Mai Voice Endpoints
Simplified AI processing for voice and chat functionality
"""

import logging
import asyncio
import uuid
import json
import base64
from typing import Optional, Dict, Any, List
from datetime import datetime

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Try to import Gemini Live API components
try:
    from google import genai as genai_live
    GEMINI_LIVE_AVAILABLE = True
except ImportError as e:
    GEMINI_LIVE_AVAILABLE = False
    print(f"❌ Gemini Live API not available: {e}")

from config import settings, SYSTEM_PROMPT
from models import (
    ChatRequest, ChatResponse, VoiceRequest,
    ConversationMemory, MessageType, SessionType, session_manager
)

logger = logging.getLogger(__name__)

class AIHandler:
    """Handles AI interactions for chat and voice"""
    
    def __init__(self):
        self.model = None
        self.voice_model = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Gemini models"""
        try:
            if settings.gemini_api_key and settings.gemini_api_key != "dummy-key-for-health-check":
                genai.configure(api_key=settings.gemini_api_key)
                
                # Initialize text chat model
                self.model = genai.GenerativeModel(
                    model_name=settings.text_chat_model,
                    system_instruction=SYSTEM_PROMPT,
                    safety_settings={
                        HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                        HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                        HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    }
                )
                
                # Initialize voice model if available
                if GEMINI_LIVE_AVAILABLE:
                    self.voice_model = genai.GenerativeModel(
                        model_name=settings.voice_chat_model,
                        system_instruction=SYSTEM_PROMPT
                    )
                
                logger.info("✅ AI models initialized successfully")
            else:
                logger.warning("⚠️ No valid Gemini API key - using mock responses")
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI models: {e}")
    
    async def generate_chat_response(self, request: ChatRequest) -> ChatResponse:
        """Generate response for text chat"""
        try:
            # Get or create session
            session_id = request.session_id or session_manager.create_session(SessionType.TEXT)
            memory = session_manager.get_session(session_id)
            
            if not memory:
                memory = session_manager.sessions[session_id] = ConversationMemory(session_id)
            
            # Add user message to memory
            memory.add_message("user", request.prompt)
            
            # Generate response
            if self.model:
                # Build conversation context
                conversation_history = memory.get_recent_messages(limit=10)
                context_messages = []
                
                for msg in conversation_history[:-1]:  # Exclude the current message
                    if msg["role"] == "user":
                        context_messages.append(f"User: {msg['content']}")
                    elif msg["role"] == "mai":
                        context_messages.append(f"Mai: {msg['content']}")
                
                # Create prompt with context
                context = "\n".join(context_messages) if context_messages else ""
                full_prompt = f"{context}\nUser: {request.prompt}\nMai:" if context else request.prompt
                
                # Generate response
                response = await asyncio.to_thread(
                    self.model.generate_content,
                    full_prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=request.temperature,
                        max_output_tokens=request.max_tokens,
                    )
                )
                
                ai_response = response.text.strip()
            else:
                # Mock response when no API key
                ai_response = f"Hello! I'm Mai, your AI assistant. You said: '{request.prompt}'. I'm currently running in demo mode without a valid API key."
            
            # Add AI response to memory
            memory.add_message("mai", ai_response)
            
            return ChatResponse(
                response=ai_response,
                session_id=session_id,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"❌ Error generating chat response: {e}")
            # Return error response
            return ChatResponse(
                response="I apologize, but I'm experiencing technical difficulties. Please try again.",
                session_id=request.session_id,
                timestamp=datetime.utcnow()
            )
    
    async def start_voice_session(self, request: VoiceRequest) -> Dict[str, Any]:
        """Start a voice chat session"""
        try:
            session_id = request.session_id or session_manager.create_session(SessionType.VOICE)
            
            # For now, return success - actual voice implementation would go here
            return {
                "status": "success",
                "session_id": session_id,
                "message": "Voice session started successfully",
                "webrtc_id": request.webrtc_id,
                "voice_name": request.voice_name
            }
            
        except Exception as e:
            logger.error(f"❌ Error starting voice session: {e}")
            return {
                "status": "error",
                "message": f"Failed to start voice session: {str(e)}"
            }
    
    async def process_voice_input(self, session_id: str, audio_data: str) -> Dict[str, Any]:
        """Process voice input and generate voice response"""
        try:
            memory = session_manager.get_session(session_id)
            if not memory:
                return {
                    "error": "Session not found",
                    "session_id": session_id
                }
            
            # For now, simulate voice processing
            # In a real implementation, you would:
            # 1. Convert audio_data to text (STT)
            # 2. Generate AI response
            # 3. Convert response to speech (TTS)
            
            # Simulate processing
            user_message = "Hello Mai, I'm speaking to you"  # This would be from STT
            
            # Generate text response
            chat_request = ChatRequest(prompt=user_message, session_id=session_id)
            response = await self.generate_chat_response(chat_request)
            
            # Generate mock audio response
            audio_response = self._generate_mock_audio_response(response.response)
            
            return {
                "audio_data": audio_response,
                "text": response.response,
                "session_id": session_id,
                "voice_name": "Aoede"
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing voice input: {e}")
            return {
                "error": f"Voice processing failed: {str(e)}",
                "session_id": session_id
            }
    
    async def end_voice_session(self, session_id: str):
        """End a voice session"""
        try:
            session_manager.end_session(session_id)
            logger.info(f"✅ Voice session {session_id} ended")
        except Exception as e:
            logger.error(f"❌ Error ending voice session: {e}")
    
    def _generate_mock_audio_response(self, text: str) -> str:
        """Generate mock audio response (base64 encoded)"""
        # This is a placeholder - in a real implementation, you would use TTS
        mock_audio = f"mock_audio_for_{len(text)}_chars"
        return base64.b64encode(mock_audio.encode()).decode()

# Global AI handler instance
ai_handler = AIHandler()
