#!/usr/bin/env python3
"""
Test script for Mai Voice Agent server
Tests basic functionality and API endpoints
"""

import asyncio
import httpx
import json
import sys
import os
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_server(base_url="http://localhost:8000"):
    """Test the server endpoints"""
    print(f"🧪 Testing Mai Voice Agent server at {base_url}")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Test 1: Health check
        try:
            print("\n1. Testing health endpoint...")
            response = await client.get(f"{base_url}/api/health")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data.get('status', 'unknown')}")
                print("   ✅ Health check passed")
            else:
                print(f"   ❌ Health check failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Health check error: {e}")
        
        # Test 2: Root endpoint
        try:
            print("\n2. Testing root API endpoint...")
            response = await client.get(f"{base_url}/api/")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Message: {data.get('message', 'unknown')}")
                print("   ✅ Root endpoint passed")
            else:
                print(f"   ❌ Root endpoint failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Root endpoint error: {e}")
        
        # Test 3: Input hook endpoint
        try:
            print("\n3. Testing input_hook endpoint...")
            test_data = {
                "webrtc_id": "test_session_123",
                "voice_name": "Aoede",
                "mode": "audio"
            }
            response = await client.post(
                f"{base_url}/api/input_hook",
                json=test_data
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response: {data.get('status', 'unknown')}")
                print("   ✅ Input hook passed")
            else:
                print(f"   ❌ Input hook failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Input hook error: {e}")
        
        # Test 4: Chat endpoint (if API key is available)
        try:
            print("\n4. Testing chat endpoint...")
            test_chat = {
                "prompt": "Hello, this is a test message",
                "session_id": "test_session_123"
            }
            response = await client.post(
                f"{base_url}/api/chat",
                json=test_chat
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Response preview: {data.get('response', 'No response')[:50]}...")
                print("   ✅ Chat endpoint passed")
            else:
                print(f"   ❌ Chat endpoint failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Chat endpoint error: {e}")
        
        # Test 5: Frontend serving
        try:
            print("\n5. Testing frontend serving...")
            response = await client.get(f"{base_url}/")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                content = response.text
                if "Mai Voice Agent" in content:
                    print("   ✅ Frontend serving passed")
                else:
                    print("   ⚠️ Frontend served but content may be incorrect")
            else:
                print(f"   ❌ Frontend serving failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Frontend serving error: {e}")

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Mai Voice Agent server")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Server URL to test (default: http://localhost:8000)")
    args = parser.parse_args()
    
    print("🚀 Mai Voice Agent Server Test")
    print("=" * 50)
    
    try:
        asyncio.run(test_server(args.url))
        print("\n" + "=" * 50)
        print("✅ Test completed!")
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")

if __name__ == "__main__":
    main()
