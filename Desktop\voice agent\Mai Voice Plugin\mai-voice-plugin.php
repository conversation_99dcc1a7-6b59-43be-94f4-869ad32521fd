<?php
/**
 * Plugin Name: <PERSON> Voice Plugin
 * Plugin URI: https://criticalfutureglobal.com
 * Description: AI Voice Chat Plugin for WordPress - Connect with Mai AI Assistant using voice chat with automatic transcription and email summaries.
 * Version: 1.0.0
 * Author: Critical Future
 * Author URI: https://criticalfutureglobal.com
 * License: GPL v2 or later
 * Text Domain: mai-voice-plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MAI_VOICE_PLUGIN_VERSION', '1.0.0');
define('MAI_VOICE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MAI_VOICE_PLUGIN_PATH', plugin_dir_path(__FILE__));

class MaiVoicePlugin {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'render_voice_chat'));
        add_action('wp_ajax_mai_voice_proxy', array($this, 'ajax_proxy'));
        add_action('wp_ajax_nopriv_mai_voice_proxy', array($this, 'ajax_proxy'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        
        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin
        load_plugin_textdomain('mai-voice-plugin', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function enqueue_scripts() {
        // Only load on frontend
        if (!is_admin()) {
            wp_enqueue_script(
                'mai-voice-plugin-js',
                MAI_VOICE_PLUGIN_URL . 'assets/mai-voice-plugin.js',
                array('jquery'),
                MAI_VOICE_PLUGIN_VERSION,
                true
            );
            
            wp_enqueue_style(
                'mai-voice-plugin-css',
                MAI_VOICE_PLUGIN_URL . 'assets/mai-voice-plugin.css',
                array(),
                MAI_VOICE_PLUGIN_VERSION
            );
            
            // Localize script with settings
            wp_localize_script('mai-voice-plugin-js', 'maiVoiceConfig', array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('mai_voice_nonce'),
                'apiEndpoint' => get_option('mai_voice_api_endpoint', ''),
                'enabled' => get_option('mai_voice_enabled', '1'),
                'position' => get_option('mai_voice_position', 'bottom-right'),
                'primaryColor' => get_option('mai_voice_primary_color', '#2563eb'),
                'secondaryColor' => get_option('mai_voice_secondary_color', '#1e40af'),
                'textColor' => get_option('mai_voice_text_color', '#ffffff'),
                'strings' => array(
                    'title' => __('Mai Assistant', 'mai-voice-plugin'),
                    'placeholder' => __('Type your message or click the microphone...', 'mai-voice-plugin'),
                    'connecting' => __('Connecting...', 'mai-voice-plugin'),
                    'recording' => __('Recording... Click to stop', 'mai-voice-plugin'),
                    'processing' => __('Processing...', 'mai-voice-plugin'),
                    'error' => __('Connection error. Please try again.', 'mai-voice-plugin'),
                    'microphoneError' => __('Microphone access denied or not available.', 'mai-voice-plugin'),
                    'sessionEnded' => __('Session ended. Summary will be emailed.', 'mai-voice-plugin')
                )
            ));
        }
    }
    
    public function render_voice_chat() {
        // Only render if enabled and API endpoint is configured
        if (get_option('mai_voice_enabled', '1') == '1' && !empty(get_option('mai_voice_api_endpoint', ''))) {
            $position = get_option('mai_voice_position', 'bottom-right');
            $primary_color = get_option('mai_voice_primary_color', '#2563eb');
            
            echo '<div id="mai-voice-chat-container" class="mai-position-' . esc_attr($position) . '" style="--mai-primary-color: ' . esc_attr($primary_color) . ';"></div>';
        }
    }
    
    public function ajax_proxy() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mai_voice_nonce')) {
            wp_die('Security check failed');
        }
        
        $api_endpoint = get_option('mai_voice_api_endpoint', '');
        if (empty($api_endpoint)) {
            wp_send_json_error('API endpoint not configured');
            return;
        }
        
        $action = sanitize_text_field($_POST['mai_action']);
        $data = $_POST['data'];
        
        // Proxy request to Mai Voice Endpoints
        $response = $this->make_api_request($api_endpoint, $action, $data);
        
        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        } else {
            wp_send_json_success($response);
        }
    }
    
    private function make_api_request($endpoint, $action, $data) {
        $url = rtrim($endpoint, '/');
        
        switch ($action) {
            case 'health':
                $url .= '/health';
                $method = 'GET';
                $body = null;
                break;
                
            case 'chat':
                $url .= '/chat';
                $method = 'POST';
                $body = json_encode($data);
                break;
                
            case 'voice_start':
                $url .= '/voice/start';
                $method = 'POST';
                $body = json_encode($data);
                break;
                
            case 'session_end':
                $url .= '/session/end';
                $method = 'POST';
                $body = json_encode($data);
                break;
                
            default:
                return new WP_Error('invalid_action', 'Invalid API action');
        }
        
        $args = array(
            'method' => $method,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'Mai Voice Plugin/' . MAI_VOICE_PLUGIN_VERSION
            ),
            'timeout' => 30
        );
        
        if ($body) {
            $args['body'] = $body;
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $decoded = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_error', 'Invalid JSON response');
        }
        
        return $decoded;
    }
    
    public function admin_menu() {
        add_options_page(
            __('Mai Voice Plugin Settings', 'mai-voice-plugin'),
            __('Mai Voice Plugin', 'mai-voice-plugin'),
            'manage_options',
            'mai-voice-plugin',
            array($this, 'admin_page')
        );
    }
    
    public function admin_init() {
        register_setting('mai_voice_settings', 'mai_voice_api_endpoint');
        register_setting('mai_voice_settings', 'mai_voice_enabled');
        register_setting('mai_voice_settings', 'mai_voice_position');
        register_setting('mai_voice_settings', 'mai_voice_primary_color');
        register_setting('mai_voice_settings', 'mai_voice_secondary_color');
        register_setting('mai_voice_settings', 'mai_voice_text_color');
        
        add_settings_section(
            'mai_voice_main_settings',
            __('Main Settings', 'mai-voice-plugin'),
            null,
            'mai-voice-plugin'
        );
        
        add_settings_field(
            'mai_voice_api_endpoint',
            __('API Endpoint URL', 'mai-voice-plugin'),
            array($this, 'api_endpoint_field'),
            'mai-voice-plugin',
            'mai_voice_main_settings'
        );
        
        add_settings_field(
            'mai_voice_enabled',
            __('Enable Voice Chat', 'mai-voice-plugin'),
            array($this, 'enabled_field'),
            'mai-voice-plugin',
            'mai_voice_main_settings'
        );
        
        add_settings_section(
            'mai_voice_appearance_settings',
            __('Appearance Settings', 'mai-voice-plugin'),
            null,
            'mai-voice-plugin'
        );
        
        add_settings_field(
            'mai_voice_position',
            __('Chat Position', 'mai-voice-plugin'),
            array($this, 'position_field'),
            'mai-voice-plugin',
            'mai_voice_appearance_settings'
        );
        
        add_settings_field(
            'mai_voice_primary_color',
            __('Primary Color', 'mai-voice-plugin'),
            array($this, 'primary_color_field'),
            'mai-voice-plugin',
            'mai_voice_appearance_settings'
        );
    }
    
    public function admin_page() {
        include MAI_VOICE_PLUGIN_PATH . 'admin/settings-page.php';
    }
    
    public function api_endpoint_field() {
        $value = get_option('mai_voice_api_endpoint', '');
        echo '<input type="url" name="mai_voice_api_endpoint" value="' . esc_attr($value) . '" class="regular-text" placeholder="https://your-mai-endpoints.railway.app" />';
        echo '<p class="description">' . __('Enter your deployed Mai Voice Endpoints URL (e.g., https://your-app.railway.app)', 'mai-voice-plugin') . '</p>';
    }
    
    public function enabled_field() {
        $value = get_option('mai_voice_enabled', '1');
        echo '<input type="checkbox" name="mai_voice_enabled" value="1" ' . checked($value, '1', false) . ' />';
        echo '<label>' . __('Enable the voice chat widget on your website', 'mai-voice-plugin') . '</label>';
    }
    
    public function position_field() {
        $value = get_option('mai_voice_position', 'bottom-right');
        $positions = array(
            'bottom-right' => __('Bottom Right', 'mai-voice-plugin'),
            'bottom-left' => __('Bottom Left', 'mai-voice-plugin'),
            'top-right' => __('Top Right', 'mai-voice-plugin'),
            'top-left' => __('Top Left', 'mai-voice-plugin')
        );
        
        echo '<select name="mai_voice_position">';
        foreach ($positions as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
    }
    
    public function primary_color_field() {
        $value = get_option('mai_voice_primary_color', '#2563eb');
        echo '<input type="color" name="mai_voice_primary_color" value="' . esc_attr($value) . '" />';
        echo '<p class="description">' . __('Choose the primary color to match your website theme', 'mai-voice-plugin') . '</p>';
    }
    
    public function activate() {
        // Set default options
        add_option('mai_voice_enabled', '1');
        add_option('mai_voice_position', 'bottom-right');
        add_option('mai_voice_primary_color', '#2563eb');
        add_option('mai_voice_secondary_color', '#1e40af');
        add_option('mai_voice_text_color', '#ffffff');
    }
    
    public function deactivate() {
        // Clean up if needed
    }
}

// Initialize the plugin
new MaiVoicePlugin();
