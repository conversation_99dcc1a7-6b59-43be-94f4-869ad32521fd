[phases.setup]
nixPkgs = [
  'python311',
  'python311Packages.pip',
  'python311Packages.setuptools',
  'python311Packages.wheel',
  'pkg-config',
  'cmake',
  'gcc',
  'portaudio',
  'alsa-lib',
  'pulseaudio',
  'ffmpeg',
  'openssl',
  'libffi',
  'libjpeg',
  'libopus',
  'libvpx',
  'curl',
  'wget'
]

[phases.install]
cmds = [
  'pip install --upgrade pip setuptools wheel',
  'pip install -r backend/requirements.txt'
]

[phases.build]
cmds = [
  'echo "Mai Voice Agent build completed"'
]

[start]
cmd = 'python backend/main.py'

[variables]
PORT = '8000'
PYTHONUNBUFFERED = '1'
PYTHONDONTWRITEBYTECODE = '1'
