<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mai Voice Agent - Connection Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: 500;
        }
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        .warning { background: rgba(255, 193, 7, 0.3); border-left: 4px solid #FFC107; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        .loading { opacity: 0.7; }
        pre { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Mai Voice Agent - Connection Test</h1>
        <p>This page tests the connection to your deployed Mai Voice Agent API.</p>
        
        <div class="test-controls">
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="testHealth()">❤️ Test Health</button>
            <button onclick="testChat()">💬 Test Chat</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = '/api';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealth() {
            addResult('🔍 Testing health endpoint...', 'info');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    addResult(`✅ Health check passed! Service: ${data.service}, Version: ${data.version}`, 'success');
                    addResult(`📊 Response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Health check failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, 'error');
            }
        }
        
        async function testChat() {
            addResult('💬 Testing chat endpoint...', 'info');
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Hello Mai, this is a test message!',
                        session_id: 'test_session_' + Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ Chat test passed! Mai responded: "${data.response}"`, 'success');
                    addResult(`📊 Full response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Chat test failed: ${response.status} ${response.statusText}`, 'error');
                    addResult(`📊 Error response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ Chat test error: ${error.message}`, 'error');
            }
        }
        
        async function testWebSocket() {
            addResult('🔌 Testing WebSocket connection...', 'info');
            try {
                const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws'}://${window.location.host}/api/ws`;
                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    addResult('✅ WebSocket connection established!', 'success');
                    ws.send(JSON.stringify({ type: 'test', message: 'Hello WebSocket!' }));
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    addResult(`📨 WebSocket message received: ${JSON.stringify(data)}`, 'success');
                    ws.close();
                };
                
                ws.onerror = (error) => {
                    addResult(`❌ WebSocket error: ${error}`, 'error');
                };
                
                ws.onclose = () => {
                    addResult('🔌 WebSocket connection closed', 'info');
                };
                
                // Close after 5 seconds if no response
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        addResult('⏰ WebSocket connection timeout', 'warning');
                    }
                }, 5000);
                
            } catch (error) {
                addResult(`❌ WebSocket test error: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting comprehensive API tests...', 'info');
            
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testChat();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testWebSocket();
            
            addResult('🏁 All tests completed!', 'info');
        }
        
        // Auto-run health check on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 500);
        });
    </script>
</body>
</html>
