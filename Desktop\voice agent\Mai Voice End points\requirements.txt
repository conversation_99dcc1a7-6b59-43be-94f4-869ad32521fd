# Core FastAPI and server dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Gemini AI SDK
google-genai>=0.8.0
google-generativeai>=0.3.2

# OpenAI Whisper for speech-to-text
openai-whisper>=20231117
torch>=2.0.0
torchaudio>=2.0.0

# Audio processing
numpy>=1.24.0
scipy>=1.10.0
librosa>=0.10.0
soundfile>=0.12.0

# Deepseek API client
openai>=1.0.0

# Email functionality
aiosmtplib>=3.0.0
email-validator>=2.0.0

# Configuration and environment management
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.4.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0

# HTTP client and async support
httpx>=0.28.1,<1.0.0
aiofiles>=23.0.0,<24.0.0

# CORS and middleware
python-multipart>=0.0.6

# WebSocket support
websockets>=13.0,<15.0

# Production server optimization
gunicorn>=21.2.0,<22.0.0

# Cloud deployment helpers
psutil>=5.9.0

# File handling (built-in modules: tempfile, io)
