#!/usr/bin/env python3
"""
Debug script for Mai Voice Agent
Helps identify configuration and dependency issues
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_environment():
    """Check environment variables"""
    print("🔍 Environment Variables:")
    print("-" * 30)
    
    env_vars = [
        "GEMINI_API_KEY",
        "PORT", 
        "HOST",
        "EMAIL_ADDRESS",
        "EMAIL_PASSWORD",
        "DEBUG"
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if var == "GEMINI_API_KEY" and value:
            # Don't show full API key
            display_value = f"{value[:8]}..." if len(value) > 8 else "***"
        elif var in ["EMAIL_PASSWORD"] and value:
            display_value = "***"
        else:
            display_value = value or "Not set"
        
        status = "✅" if value else "❌"
        print(f"{status} {var}: {display_value}")

def check_dependencies():
    """Check if required dependencies are available"""
    print("\n🔍 Dependencies:")
    print("-" * 30)
    
    dependencies = [
        ("fastapi", "FastAPI web framework"),
        ("uvicorn", "ASGI server"),
        ("google.genai", "Google Gemini AI"),
        ("fastrtc", "WebRTC support"),
        ("PIL", "Image processing"),
        ("numpy", "Numerical computing"),
        ("pydantic", "Data validation"),
        ("httpx", "HTTP client"),
        ("websockets", "WebSocket support")
    ]
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module}: {description}")
        except ImportError as e:
            print(f"❌ {module}: {description} - {e}")

def check_files():
    """Check if required files exist"""
    print("\n🔍 Required Files:")
    print("-" * 30)
    
    files = [
        "railway_server.py",
        "backend/routes.py",
        "backend/config.py", 
        "backend/models.py",
        "backend/ai_handlers.py",
        "backend/webrtc_handler.py",
        "backend/email_service.py",
        "frontend/index.html",
        "frontend/script.js",
        "frontend/styles.css"
    ]
    
    for file_path in files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")

def check_backend_imports():
    """Check if backend modules can be imported"""
    print("\n🔍 Backend Module Imports:")
    print("-" * 30)
    
    # Add backend to path
    backend_path = Path(__file__).parent / "backend"
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
    
    modules = [
        "config",
        "models", 
        "routes",
        "ai_handlers",
        "webrtc_handler",
        "email_service"
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - {e}")
        except Exception as e:
            print(f"⚠️ {module} - {e}")

def test_basic_server():
    """Test if basic server can start"""
    print("\n🔍 Basic Server Test:")
    print("-" * 30)
    
    try:
        from fastapi import FastAPI
        app = FastAPI()
        
        @app.get("/test")
        def test_endpoint():
            return {"status": "ok"}
        
        print("✅ Basic FastAPI app creation successful")
        return True
    except Exception as e:
        print(f"❌ Basic FastAPI app creation failed: {e}")
        return False

def main():
    """Main debug function"""
    print("🔧 Mai Voice Agent Debug Tool")
    print("=" * 50)
    
    check_environment()
    check_dependencies()
    check_files()
    check_backend_imports()
    test_basic_server()
    
    print("\n" + "=" * 50)
    print("🔧 Debug completed!")
    print("\n💡 Tips:")
    print("- Set GEMINI_API_KEY for AI functionality")
    print("- Install missing dependencies with: pip install -r backend/requirements.txt")
    print("- Check file paths are correct")

if __name__ == "__main__":
    main()
