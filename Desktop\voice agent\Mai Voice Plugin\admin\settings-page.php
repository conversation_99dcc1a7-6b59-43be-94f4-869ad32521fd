<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="mai-voice-admin-header">
        <div class="mai-voice-logo">
            <h2>🎤 Mai Voice Plugin</h2>
            <p>AI Voice Chat Integration for WordPress</p>
        </div>
        <div class="mai-voice-version">
            <span>Version <?php echo MAI_VOICE_PLUGIN_VERSION; ?></span>
        </div>
    </div>

    <?php
    // Show admin notices
    if (isset($_GET['settings-updated'])) {
        add_settings_error('mai_voice_messages', 'mai_voice_message', __('Settings Saved', 'mai-voice-plugin'), 'updated');
    }
    settings_errors('mai_voice_messages');
    ?>

    <div class="mai-voice-admin-content">
        <div class="mai-voice-main-settings">
            <form method="post" action="options.php">
                <?php
                settings_fields('mai_voice_settings');
                do_settings_sections('mai-voice-plugin');
                ?>
                
                <div class="mai-voice-settings-section">
                    <h3><?php _e('API Configuration', 'mai-voice-plugin'); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('API Endpoint URL', 'mai-voice-plugin'); ?></th>
                            <td>
                                <?php $this->api_endpoint_field(); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Enable Voice Chat', 'mai-voice-plugin'); ?></th>
                            <td>
                                <?php $this->enabled_field(); ?>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="mai-voice-settings-section">
                    <h3><?php _e('Appearance Settings', 'mai-voice-plugin'); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Chat Position', 'mai-voice-plugin'); ?></th>
                            <td>
                                <?php $this->position_field(); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Primary Color', 'mai-voice-plugin'); ?></th>
                            <td>
                                <?php $this->primary_color_field(); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Secondary Color', 'mai-voice-plugin'); ?></th>
                            <td>
                                <input type="color" name="mai_voice_secondary_color" value="<?php echo esc_attr(get_option('mai_voice_secondary_color', '#1e40af')); ?>" />
                                <p class="description"><?php _e('Secondary color for hover effects and accents', 'mai-voice-plugin'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Text Color', 'mai-voice-plugin'); ?></th>
                            <td>
                                <input type="color" name="mai_voice_text_color" value="<?php echo esc_attr(get_option('mai_voice_text_color', '#ffffff')); ?>" />
                                <p class="description"><?php _e('Text color for the chat interface', 'mai-voice-plugin'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button(); ?>
            </form>
        </div>

        <div class="mai-voice-sidebar">
            <div class="mai-voice-info-box">
                <h3><?php _e('Setup Instructions', 'mai-voice-plugin'); ?></h3>
                <ol>
                    <li><?php _e('Deploy your Mai Voice Endpoints to Railway', 'mai-voice-plugin'); ?></li>
                    <li><?php _e('Copy your Railway app URL', 'mai-voice-plugin'); ?></li>
                    <li><?php _e('Paste the URL in the API Endpoint field above', 'mai-voice-plugin'); ?></li>
                    <li><?php _e('Enable the voice chat and save settings', 'mai-voice-plugin'); ?></li>
                    <li><?php _e('The voice chat widget will appear on your website', 'mai-voice-plugin'); ?></li>
                </ol>
            </div>

            <div class="mai-voice-info-box">
                <h3><?php _e('How It Works', 'mai-voice-plugin'); ?></h3>
                <ul>
                    <li>🎤 <?php _e('Users click the voice chat button', 'mai-voice-plugin'); ?></li>
                    <li>🗣️ <?php _e('Voice is transcribed using Whisper AI', 'mai-voice-plugin'); ?></li>
                    <li>🤖 <?php _e('Mai AI responds using Gemini', 'mai-voice-plugin'); ?></li>
                    <li>📝 <?php _e('Conversation is summarized by Deepseek', 'mai-voice-plugin'); ?></li>
                    <li>📧 <?php _e('Summary is emailed to you automatically', 'mai-voice-plugin'); ?></li>
                </ul>
            </div>

            <div class="mai-voice-info-box">
                <h3><?php _e('Test Connection', 'mai-voice-plugin'); ?></h3>
                <button type="button" id="mai-test-connection" class="button button-secondary">
                    <?php _e('Test API Connection', 'mai-voice-plugin'); ?>
                </button>
                <div id="mai-test-result" style="margin-top: 10px;"></div>
            </div>

            <div class="mai-voice-info-box">
                <h3><?php _e('Support', 'mai-voice-plugin'); ?></h3>
                <p><?php _e('Need help? Contact Critical Future:', 'mai-voice-plugin'); ?></p>
                <p>
                    <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    <strong>Website:</strong> <a href="https://criticalfutureglobal.com" target="_blank">criticalfutureglobal.com</a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.mai-voice-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.mai-voice-logo h2 {
    margin: 0;
    color: white;
}

.mai-voice-logo p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.mai-voice-version {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
}

.mai-voice-admin-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.mai-voice-settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.mai-voice-settings-section h3 {
    margin-top: 0;
    color: #2563eb;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 10px;
}

.mai-voice-info-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.mai-voice-info-box h3 {
    margin-top: 0;
    color: #2563eb;
}

.mai-voice-info-box ol,
.mai-voice-info-box ul {
    padding-left: 20px;
}

.mai-voice-info-box li {
    margin-bottom: 8px;
}

#mai-test-result.success {
    color: #059669;
    font-weight: bold;
}

#mai-test-result.error {
    color: #dc2626;
    font-weight: bold;
}

@media (max-width: 768px) {
    .mai-voice-admin-content {
        grid-template-columns: 1fr;
    }
    
    .mai-voice-admin-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#mai-test-connection').click(function() {
        var button = $(this);
        var result = $('#mai-test-result');
        var endpoint = $('input[name="mai_voice_api_endpoint"]').val();
        
        if (!endpoint) {
            result.html('<span class="error">Please enter an API endpoint URL first.</span>');
            return;
        }
        
        button.prop('disabled', true).text('Testing...');
        result.html('Testing connection...');
        
        $.post(ajaxurl, {
            action: 'mai_voice_proxy',
            nonce: '<?php echo wp_create_nonce('mai_voice_nonce'); ?>',
            mai_action: 'health',
            data: {}
        }, function(response) {
            if (response.success) {
                result.html('<span class="success">✅ Connection successful! API is responding.</span>');
            } else {
                result.html('<span class="error">❌ Connection failed: ' + response.data + '</span>');
            }
        }).fail(function() {
            result.html('<span class="error">❌ Connection failed: Network error</span>');
        }).always(function() {
            button.prop('disabled', false).text('Test API Connection');
        });
    });
});
</script>
